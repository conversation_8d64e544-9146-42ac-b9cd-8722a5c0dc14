<Window x:Class="DriverManagementSystem.Views.ProfessionalMessageDocumentationReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
        Title="تقرير توثيق الرسائل النصية - النسخة الاحترافية"
        Height="900" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <converters:FileNameConverter x:Key="FileNameConverter"/>
    </Window.Resources>

    <Grid Background="White">
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <Border Margin="20" Padding="30" Background="White" BorderBrush="#CCCCCC" BorderThickness="1">
                <StackPanel>
                    <!-- Header Section -->
                    <Grid Margin="0,0,0,30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Logo -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                            <Image Source="C:\Users\<USER>\Desktop\sys\SFD\logos\sfd.png" 
                                   Width="80" Height="80" 
                                   HorizontalAlignment="Left"/>
                            <TextBlock Text="الصندوق الاجتماعي للتنمية" 
                                     FontSize="10" 
                                     HorizontalAlignment="Left" 
                                     Margin="0,5,0,0"/>
                        </StackPanel>

                        <!-- Title -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="توثيق الرسائل النصية للنزول الميداني" 
                                     FontSize="20" 
                                     FontWeight="Bold" 
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,10"/>
                            <Rectangle Height="3" Fill="#333333" Margin="20,0"/>
                        </StackPanel>

                        <!-- Report Info -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                            <TextBlock Text="{Binding ReportNumber, StringFormat='رقم التقرير: {0}'}" 
                                     FontSize="12" 
                                     FontWeight="Bold"/>
                            <TextBlock Text="{Binding VisitNumber, StringFormat='رقم الزيارة: {0}'}" 
                                     FontSize="12" 
                                     FontWeight="Bold"
                                     Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding ContractNumber, StringFormat='رقم العقد: {0}'}" 
                                     FontSize="12" 
                                     Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding DocumentationDate, StringFormat='التاريخ: {0:dd/MM/yyyy}'}" 
                                     FontSize="12" 
                                     Margin="0,5,0,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Main Content -->
                    <StackPanel>
                        <!-- Officers Section -->
                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="20" Margin="0,0,0,20" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="📋 المختصين بالبترول" 
                                         FontSize="18" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,20"
                                         HorizontalAlignment="Center"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                        <TextBlock Text="المختص الأول:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="15" MinHeight="50" CornerRadius="3">
                                            <TextBlock Text="{Binding FirstOfficer}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="13"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Margin="5,0">
                                        <TextBlock Text="المختص الثاني:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="15" MinHeight="50" CornerRadius="3">
                                            <TextBlock Text="{Binding SecondOfficer}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="13"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" Margin="10,0,0,0">
                                        <TextBlock Text="المختص الثالث:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="15" MinHeight="50" CornerRadius="3">
                                            <TextBlock Text="{Binding ThirdOfficer}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="13"/>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Images Section -->
                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="20" Margin="0,0,0,20" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="🖼️ الصور المرفقة" 
                                         FontSize="18" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,20"
                                         HorizontalAlignment="Center"/>

                                <ItemsControl ItemsSource="{Binding ImagePaths}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="#DDDDDD" BorderThickness="2" Margin="10" CornerRadius="5" Padding="5">
                                                <StackPanel>
                                                    <Image Source="{Binding}" 
                                                           Width="220" 
                                                           Height="165" 
                                                           Stretch="UniformToFill"/>
                                                    <TextBlock Text="{Binding Converter={StaticResource FileNameConverter}}" 
                                                             FontSize="10" 
                                                             HorizontalAlignment="Center"
                                                             Margin="0,5,0,0"
                                                             TextTrimming="CharacterEllipsis"
                                                             MaxWidth="220"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <!-- If no images -->
                                <TextBlock Text="لا توجد صور مرفقة" 
                                         HorizontalAlignment="Center" 
                                         FontStyle="Italic" 
                                         Foreground="Gray"
                                         FontSize="14"
                                         Margin="0,20,0,0">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ImagePaths.Count}" Value="0">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <!-- Other Attachments Section -->
                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="20" Margin="0,0,0,20" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="📎 المرفقات الأخرى" 
                                         FontSize="18" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,15"
                                         HorizontalAlignment="Center"/>

                                <ItemsControl ItemsSource="{Binding OtherAttachments}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="15" Margin="0,0,0,10" CornerRadius="3">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Text="📄" Grid.Column="0" FontSize="20" VerticalAlignment="Center" Margin="0,0,15,0"/>
                                                    
                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="{Binding FileName}" FontWeight="Bold" FontSize="13"/>
                                                        <TextBlock Text="{Binding Description}" FontSize="11" Foreground="Gray"/>
                                                    </StackPanel>

                                                    <TextBlock Text="{Binding FileSize, StringFormat=N0}" 
                                                             Grid.Column="2" 
                                                             FontSize="11" 
                                                             Foreground="Gray" 
                                                             VerticalAlignment="Center"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <!-- If no other attachments -->
                                <TextBlock Text="لا توجد مرفقات أخرى" 
                                         HorizontalAlignment="Center" 
                                         FontStyle="Italic" 
                                         Foreground="Gray"
                                         FontSize="12">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding OtherAttachments.Count}" Value="0">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <!-- Notes Section -->
                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="20" Margin="0,0,0,20" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="📝 ملاحظات إضافية" 
                                         FontSize="18" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,15"
                                         HorizontalAlignment="Center"/>

                                <Border BorderBrush="#EEEEEE" BorderThickness="1" Padding="20" MinHeight="100" CornerRadius="3">
                                    <TextBlock Text="{Binding Notes}" 
                                             TextWrapping="Wrap" 
                                             LineHeight="22"
                                             FontSize="13"/>
                                </Border>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Section -->
                        <Border BorderBrush="#17A2B8" BorderThickness="2" Padding="20" Margin="0,0,0,20" CornerRadius="5" Background="#F0F8FF">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات التوثيق" 
                                         FontSize="16" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,15"
                                         HorizontalAlignment="Center"
                                         Foreground="#17A2B8"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding ImagePaths.Count}" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#28A745"/>
                                        <TextBlock Text="عدد الصور" FontSize="12" HorizontalAlignment="Center"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding OtherAttachments.Count}" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#FFC107"/>
                                        <TextBlock Text="المرفقات الأخرى" FontSize="12" HorizontalAlignment="Center"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding DocumentationDate, StringFormat=dd/MM}" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#DC3545"/>
                                        <TextBlock Text="تاريخ التوثيق" FontSize="12" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Signatures Section -->
                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="20" CornerRadius="5">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                    <TextBlock Text="إعداد:" FontWeight="Bold" FontSize="14" Margin="0,0,0,40"/>
                                    <Rectangle Height="2" Width="180" Fill="#333333" Margin="0,0,0,8"/>
                                    <TextBlock Text="المختص بالبترول" FontSize="12"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                    <TextBlock Text="مراجعة:" FontWeight="Bold" FontSize="14" Margin="0,0,0,40"/>
                                    <Rectangle Height="2" Width="180" Fill="#333333" Margin="0,0,0,8"/>
                                    <TextBlock Text="رئيس القسم" FontSize="12"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                    <TextBlock Text="اعتماد:" FontWeight="Bold" FontSize="14" Margin="0,0,0,40"/>
                                    <Rectangle Height="2" Width="180" Fill="#333333" Margin="0,0,0,8"/>
                                    <TextBlock Text="مدير المكتب" FontSize="12"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </StackPanel>

                    <!-- Footer -->
                    <StackPanel Margin="0,30,0,0" HorizontalAlignment="Center">
                        <TextBlock Text="صفحة (1) من (1)" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </ScrollViewer>
    </Grid>
</Window>
