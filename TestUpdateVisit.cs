using System;
using System.Threading.Tasks;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem
{
    /// <summary>
    /// فئة اختبار لتشخيص مشكلة تحديث الزيارات الميدانية
    /// </summary>
    public class TestUpdateVisit
    {
        private readonly SqliteDataService _dataService;

        public TestUpdateVisit()
        {
            _dataService = new SqliteDataService();
        }

        /// <summary>
        /// اختبار تحديث زيارة ميدانية موجودة
        /// </summary>
        public async Task<bool> TestUpdateExistingVisit()
        {
            try
            {
                Console.WriteLine("🔍 بدء اختبار تحديث الزيارة الميدانية...");

                // 1. جلب جميع الزيارات الموجودة
                var visits = await _dataService.GetFieldVisitsAsync();
                Console.WriteLine($"📊 عدد الزيارات الموجودة: {visits.Count}");

                if (visits.Count == 0)
                {
                    Console.WriteLine("⚠️ لا توجد زيارات للاختبار");
                    return false;
                }

                // 2. اختيار أول زيارة للاختبار
                var visitToUpdate = visits[0];
                Console.WriteLine($"🎯 اختبار تحديث الزيارة: {visitToUpdate.VisitNumber} (ID: {visitToUpdate.Id})");

                // 3. عمل تعديل بسيط على الزيارة
                var originalPurpose = visitToUpdate.MissionPurpose;
                visitToUpdate.MissionPurpose = $"{originalPurpose} - تم التحديث في {DateTime.Now:HH:mm:ss}";

                Console.WriteLine($"📝 تعديل مهمة النزول من: '{originalPurpose}'");
                Console.WriteLine($"📝 إلى: '{visitToUpdate.MissionPurpose}'");

                // 4. محاولة التحديث
                Console.WriteLine("🔄 محاولة تحديث الزيارة...");
                var success = await _dataService.UpdateFieldVisitAsync(visitToUpdate);

                if (success)
                {
                    Console.WriteLine("✅ تم تحديث الزيارة بنجاح!");
                    
                    // 5. التحقق من التحديث
                    var updatedVisits = await _dataService.GetFieldVisitsAsync();
                    var updatedVisit = updatedVisits.Find(v => v.Id == visitToUpdate.Id);
                    
                    if (updatedVisit != null && updatedVisit.MissionPurpose == visitToUpdate.MissionPurpose)
                    {
                        Console.WriteLine("✅ تم التحقق من التحديث بنجاح!");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine("❌ فشل في التحقق من التحديث");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("❌ فشل في تحديث الزيارة");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// اختبار تشخيصي شامل
        /// </summary>
        public async Task RunDiagnosticTest()
        {
            Console.WriteLine("🚀 بدء الاختبار التشخيصي الشامل...");
            Console.WriteLine(new string('=', 50));

            try
            {
                // اختبار الاتصال بقاعدة البيانات
                Console.WriteLine("1️⃣ اختبار الاتصال بقاعدة البيانات...");
                var visits = await _dataService.GetFieldVisitsAsync();
                Console.WriteLine($"✅ تم الاتصال بنجاح - عدد الزيارات: {visits.Count}");

                if (visits.Count > 0)
                {
                    // اختبار تحديث الزيارة
                    Console.WriteLine("\n2️⃣ اختبار تحديث الزيارة...");
                    var updateResult = await TestUpdateExistingVisit();
                    Console.WriteLine($"نتيجة اختبار التحديث: {(updateResult ? "نجح ✅" : "فشل ❌")}");
                }

                Console.WriteLine("\n" + new string('=', 50));
                Console.WriteLine("🏁 انتهى الاختبار التشخيصي");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار التشخيصي: {ex.Message}");
            }
        }
    }
}
