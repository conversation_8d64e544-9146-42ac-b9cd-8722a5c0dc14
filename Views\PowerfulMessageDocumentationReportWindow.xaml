<Window x:Class="DriverManagementSystem.Views.PowerfulMessageDocumentationReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
        Title="🚀 تقرير توثيق الرسائل النصية - النظام القوي"
        Height="900" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <converters:FileNameConverter x:Key="FileNameConverter"/>
    </Window.Resources>

    <Grid Background="White">
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <Border Margin="20" Padding="40" Background="White" BorderBrush="#CCCCCC" BorderThickness="2">
                <StackPanel>
                    <!-- Header Section -->
                    <Grid Margin="0,0,0,40">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Logo -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                            <Image Source="C:\Users\<USER>\Desktop\sys\SFD\logos\sfd.png" 
                                   Width="90" Height="90" 
                                   HorizontalAlignment="Left"/>
                            <TextBlock Text="الصندوق الاجتماعي للتنمية" 
                                     FontSize="11" 
                                     HorizontalAlignment="Left" 
                                     Margin="0,8,0,0"
                                     FontWeight="Bold"/>
                        </StackPanel>

                        <!-- Title -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="🚀 توثيق الرسائل النصية للنزول الميداني" 
                                     FontSize="22" 
                                     FontWeight="Bold" 
                                     HorizontalAlignment="Center"
                                     Foreground="#2C3E50"
                                     Margin="0,0,0,15"/>
                            <Rectangle Height="4" Fill="#667eea" Margin="30,0"/>
                        </StackPanel>

                        <!-- Report Info -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                            <Border Background="#F8F9FA" Padding="15" CornerRadius="8" BorderBrush="#DEE2E6" BorderThickness="1">
                                <StackPanel>
                                    <TextBlock Text="{Binding ReportNumber, StringFormat='رقم التقرير: {0}'}" 
                                             FontSize="13" 
                                             FontWeight="Bold"
                                             Foreground="#495057"/>
                                    <TextBlock Text="{Binding VisitNumber, StringFormat='رقم الزيارة: {0}'}" 
                                             FontSize="13" 
                                             FontWeight="Bold"
                                             Foreground="#495057"
                                             Margin="0,8,0,0"/>
                                    <TextBlock Text="{Binding DocumentationDate, StringFormat='التاريخ: {0:dd/MM/yyyy}'}" 
                                             FontSize="12" 
                                             Foreground="#6C757D"
                                             Margin="0,8,0,0"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>

                    <!-- Main Content -->
                    <StackPanel>
                        <!-- Visit Conductors Section -->
                        <Border BorderBrush="#667eea" BorderThickness="3" Padding="25" Margin="0,0,0,25" CornerRadius="10" Background="#F8F9FF">
                            <StackPanel>
                                <TextBlock Text="👥 القائمين بالزيارة" 
                                         FontSize="20" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,20"
                                         HorizontalAlignment="Center"
                                         Foreground="#667eea"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                        <TextBlock Text="القائم الأول:" FontWeight="Bold" FontSize="15" Margin="0,0,0,10" Foreground="#495057"/>
                                        <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="18" MinHeight="60" CornerRadius="8" Background="White">
                                            <TextBlock Text="{Binding VisitConductor1}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="14"
                                                     Foreground="#212529"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Margin="7,0">
                                        <TextBlock Text="القائم الثاني:" FontWeight="Bold" FontSize="15" Margin="0,0,0,10" Foreground="#495057"/>
                                        <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="18" MinHeight="60" CornerRadius="8" Background="White">
                                            <TextBlock Text="{Binding VisitConductor2}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="14"
                                                     Foreground="#212529"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" Margin="15,0,0,0">
                                        <TextBlock Text="القائم الثالث:" FontWeight="Bold" FontSize="15" Margin="0,0,0,10" Foreground="#495057"/>
                                        <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="18" MinHeight="60" CornerRadius="8" Background="White">
                                            <TextBlock Text="{Binding VisitConductor3}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="14"
                                                     Foreground="#212529"/>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Fuel Specialists Section -->
                        <Border BorderBrush="#28A745" BorderThickness="3" Padding="25" Margin="0,0,0,25" CornerRadius="10" Background="#F8FFF9">
                            <StackPanel>
                                <TextBlock Text="⛽ المختصين بالبترول" 
                                         FontSize="20" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,20"
                                         HorizontalAlignment="Center"
                                         Foreground="#28A745"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                        <TextBlock Text="المختص الأول:" FontWeight="Bold" FontSize="15" Margin="0,0,0,10" Foreground="#495057"/>
                                        <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="18" MinHeight="60" CornerRadius="8" Background="White">
                                            <TextBlock Text="{Binding FirstOfficer}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="14"
                                                     Foreground="#212529"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Margin="7,0">
                                        <TextBlock Text="المختص الثاني:" FontWeight="Bold" FontSize="15" Margin="0,0,0,10" Foreground="#495057"/>
                                        <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="18" MinHeight="60" CornerRadius="8" Background="White">
                                            <TextBlock Text="{Binding SecondOfficer}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="14"
                                                     Foreground="#212529"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" Margin="15,0,0,0">
                                        <TextBlock Text="المختص الثالث:" FontWeight="Bold" FontSize="15" Margin="0,0,0,10" Foreground="#495057"/>
                                        <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="18" MinHeight="60" CornerRadius="8" Background="White">
                                            <TextBlock Text="{Binding ThirdOfficer}" 
                                                     TextWrapping="Wrap" 
                                                     VerticalAlignment="Center"
                                                     FontSize="14"
                                                     Foreground="#212529"/>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Images Section -->
                        <Border BorderBrush="#17A2B8" BorderThickness="3" Padding="25" Margin="0,0,0,25" CornerRadius="10" Background="#F0FDFF">
                            <StackPanel>
                                <TextBlock Text="🖼️ الصور المرفقة" 
                                         FontSize="20" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,25"
                                         HorizontalAlignment="Center"
                                         Foreground="#17A2B8"/>

                                <ItemsControl ItemsSource="{Binding ImagePaths}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="#DEE2E6" BorderThickness="3" Margin="15" CornerRadius="10" Padding="8" Background="White">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.4"/>
                                                </Border.Effect>
                                                <StackPanel>
                                                    <Image Source="{Binding}"
                                                           Width="240"
                                                           Height="180"
                                                           Stretch="UniformToFill"/>
                                                    <TextBlock Text="{Binding Converter={StaticResource FileNameConverter}}" 
                                                             FontSize="11" 
                                                             HorizontalAlignment="Center"
                                                             Margin="0,8,0,0"
                                                             TextTrimming="CharacterEllipsis"
                                                             MaxWidth="240"
                                                             FontWeight="Bold"
                                                             Foreground="#495057"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <!-- If no images -->
                                <StackPanel HorizontalAlignment="Center" Margin="0,30,0,0">
                                    <StackPanel.Style>
                                        <Style TargetType="StackPanel">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ImagePaths.Count}" Value="0">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </StackPanel.Style>
                                    <TextBlock Text="📷" FontSize="60" Foreground="#DEE2E6" HorizontalAlignment="Center"/>
                                    <TextBlock Text="لا توجد صور مرفقة" 
                                             HorizontalAlignment="Center" 
                                             FontStyle="Italic" 
                                             Foreground="#6C757D"
                                             FontSize="16"
                                             Margin="0,10,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Notes Section -->
                        <Border BorderBrush="#FFC107" BorderThickness="3" Padding="25" Margin="0,0,0,25" CornerRadius="10" Background="#FFFDF0">
                            <StackPanel>
                                <TextBlock Text="📝 ملاحظات إضافية" 
                                         FontSize="20" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,20"
                                         HorizontalAlignment="Center"
                                         Foreground="#FFC107"/>

                                <Border BorderBrush="#DEE2E6" BorderThickness="2" Padding="25" MinHeight="120" CornerRadius="8" Background="White">
                                    <TextBlock Text="{Binding Notes}" 
                                             TextWrapping="Wrap" 
                                             LineHeight="24"
                                             FontSize="14"
                                             Foreground="#212529"/>
                                </Border>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Section -->
                        <Border BorderBrush="#6F42C1" BorderThickness="3" Padding="25" Margin="0,0,0,30" CornerRadius="10" Background="#F8F6FF">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات التوثيق" 
                                         FontSize="18" 
                                         FontWeight="Bold" 
                                         Margin="0,0,0,20"
                                         HorizontalAlignment="Center"
                                         Foreground="#6F42C1"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding ImagePaths.Count}" FontSize="32" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#28A745"/>
                                        <TextBlock Text="عدد الصور" FontSize="14" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding OtherAttachments.Count}" FontSize="32" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#FFC107"/>
                                        <TextBlock Text="المرفقات الأخرى" FontSize="14" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding DocumentationDate, StringFormat=dd/MM}" FontSize="32" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#DC3545"/>
                                        <TextBlock Text="تاريخ التوثيق" FontSize="14" HorizontalAlignment="Center" Foreground="#495057"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Signatures Section -->
                        <Border BorderBrush="#343A40" BorderThickness="3" Padding="25" CornerRadius="10" Background="#F8F9FA">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                    <TextBlock Text="إعداد:" FontWeight="Bold" FontSize="16" Margin="0,0,0,50" Foreground="#495057"/>
                                    <Rectangle Height="3" Width="200" Fill="#343A40" Margin="0,0,0,10"/>
                                    <TextBlock Text="المختص بالبترول" FontSize="13" Foreground="#6C757D"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                    <TextBlock Text="مراجعة:" FontWeight="Bold" FontSize="16" Margin="0,0,0,50" Foreground="#495057"/>
                                    <Rectangle Height="3" Width="200" Fill="#343A40" Margin="0,0,0,10"/>
                                    <TextBlock Text="رئيس القسم" FontSize="13" Foreground="#6C757D"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                    <TextBlock Text="اعتماد:" FontWeight="Bold" FontSize="16" Margin="0,0,0,50" Foreground="#495057"/>
                                    <Rectangle Height="3" Width="200" Fill="#343A40" Margin="0,0,0,10"/>
                                    <TextBlock Text="مدير المكتب" FontSize="13" Foreground="#6C757D"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </StackPanel>

                    <!-- Footer -->
                    <StackPanel Margin="0,40,0,0" HorizontalAlignment="Center">
                        <TextBlock Text="صفحة (1) من (1)" 
                                 FontSize="14" 
                                 HorizontalAlignment="Center"
                                 Foreground="#6C757D"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </ScrollViewer>
    </Grid>
</Window>
