using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Prism.Commands;
using Prism.Mvvm;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.ViewModels
{
    public class DropDataViewModel : BindableBase
    {
        private readonly DataService _dataService;
        private FieldVisit? _selectedFieldVisit;
        private string _numericInput = string.Empty;
        private string _visitNumber = string.Empty;
        private string _driverContract = string.Empty;
        private bool _isVisitNumberDuplicate = false;
        private DateTime _addDate = DateTime.Now;
        private string _hijriDate = string.Empty;
        private DateTime _departureDate = DateTime.Now;
        private DateTime _returnDate = DateTime.Now;
        private int _daysCount = 1;
        private string _missionPurpose = string.Empty;
        private Sector? _selectedSector;
        private int _visitorsCount = 1;
        private string _statusMessage = "النظام جاهز للاستخدام";
        private string _saveButtonText = "إضافة الزيارة";
        private string _currentDate = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
        private string _currentTime = DateTime.Now.ToString("HH:mm:ss");
        private int _projectsCount = 1;
        private string _securityRoute = string.Empty;
        private string _visitNotes = string.Empty;

        public DropDataViewModel()
        {


            try
            {
                System.Diagnostics.Debug.WriteLine("Initializing DropDataViewModel...");

                _dataService = new DataService();
                // Subscribe to database refresh event
                DriverManagementSystem.ViewModels.DashboardViewModel.DatabaseRefreshed += OnDatabaseRefreshed;
                System.Diagnostics.Debug.WriteLine("DataService created");

                // Initialize collections
                FieldVisits = new ObservableCollection<FieldVisit>();
                Sectors = new ObservableCollection<Sector>();
                SectorOfficers = new ObservableCollection<Officer>();
                VisitorInputs = new ObservableCollection<VisitorInput>();
                ItineraryDays = new ObservableCollection<ItineraryDay>();
                Projects = new ObservableCollection<Project>();
                ProjectInputs = new ObservableCollection<ProjectInput>();
                System.Diagnostics.Debug.WriteLine("Collections initialized");

                // Initialize commands
                AddCommand = new DelegateCommand(AddFieldVisit, CanExecuteAdd);
                SaveCommand = new DelegateCommand(SaveOrUpdateFieldVisit, CanExecuteSaveOrUpdate);
                DeleteCommand = new DelegateCommand(DeleteFieldVisit, CanExecuteDelete);
                IncreaseVisitorsCommand = new DelegateCommand(IncreaseVisitors);
                DecreaseVisitorsCommand = new DelegateCommand(DecreaseVisitors, CanDecreaseVisitors);
                ClearAllDataCommand = new DelegateCommand(ClearAllData);
                IncreaseProjectsCommand = new DelegateCommand(IncreaseProjects);
                DecreaseProjectsCommand = new DelegateCommand(DecreaseProjects, CanDecreaseProjects);
                AddNewProjectCommand = new DelegateCommand(AddNewProject);
                SearchProjectCommand = new DelegateCommand<ProjectInput>(SearchProjectByNumber);
                IncreaseItineraryCommand = new DelegateCommand(IncreaseItinerary);
                DecreaseItineraryCommand = new DelegateCommand(DecreaseItinerary, CanDecreaseItinerary);
                ImportExcelCommand = new DelegateCommand(ImportExcelFile);
                ShowImportStatisticsCommand = new DelegateCommand(ShowImportStatistics);
                ShowSystemDashboardCommand = new DelegateCommand(ShowSystemDashboard);
                ShowBatchImportCommand = new DelegateCommand(ShowBatchImport);

                System.Diagnostics.Debug.WriteLine("Commands initialized");

                // Load initial data
                LoadData();
                System.Diagnostics.Debug.WriteLine("Data loading initiated");

                // Generate initial visit number and driver contract
                GenerateVisitNumber();
                GenerateDriverContract();
                System.Diagnostics.Debug.WriteLine("Visit number and code generated");

                // Set initial hijri date
                UpdateHijriDate();
                System.Diagnostics.Debug.WriteLine("Hijri date updated");

                // Calculate initial days
                CalculateDays();
                System.Diagnostics.Debug.WriteLine("Days calculated");

                // Initialize visitor inputs
                UpdateVisitorInputs();
                System.Diagnostics.Debug.WriteLine("Visitor inputs updated");

                // Start time update timer
                StartTimeUpdateTimer();
                System.Diagnostics.Debug.WriteLine("Timer started");

                // Subscribe to edit visit requests
                EditVisitRequested += OnEditVisitRequested;

                // Update status
                StatusMessage = "النظام جاهز - يمكنك إضافة زيارة جديدة";
                System.Diagnostics.Debug.WriteLine("DropDataViewModel initialization completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DropDataViewModel constructor: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Initialize minimal required properties to prevent crashes
                FieldVisits = new ObservableCollection<FieldVisit>();
                Sectors = new ObservableCollection<Sector>();
                SectorOfficers = new ObservableCollection<Officer>();
                VisitorInputs = new ObservableCollection<VisitorInput>();

                StatusMessage = $"خطأ في التهيئة: {ex.Message}";

                // Initialize basic commands
                AddCommand = new DelegateCommand(() => { }, () => false);
                SaveCommand = new DelegateCommand(() => { }, () => false);
                DeleteCommand = new DelegateCommand(() => { }, () => false);
                IncreaseVisitorsCommand = new DelegateCommand(() => { });
                DecreaseVisitorsCommand = new DelegateCommand(() => { }, () => false);
            }
        }

        #region Properties

        public ObservableCollection<FieldVisit> FieldVisits { get; }
        public ObservableCollection<Sector> Sectors { get; }
        public ObservableCollection<Officer> SectorOfficers { get; }
        public ObservableCollection<VisitorInput> VisitorInputs { get; }
        public ObservableCollection<ItineraryDay> ItineraryDays { get; }
        public ObservableCollection<Project> Projects { get; }
        public ObservableCollection<ProjectInput> ProjectInputs { get; }

        public FieldVisit? SelectedFieldVisit
        {
            get => _selectedFieldVisit;
            set
            {
                SetProperty(ref _selectedFieldVisit, value);
                _ = LoadFieldVisitDetailsAsync(); // استدعاء async
                RaiseCanExecuteChanged();
            }
        }

        public string NumericInput
        {
            get => _numericInput;
            set
            {
                SetProperty(ref _numericInput, value);
                RaiseCanExecuteChanged();
            }
        }

        public string VisitNumber
        {
            get => _visitNumber;
            set
            {
                SetProperty(ref _visitNumber, value);
                CheckVisitNumberDuplicate();
                RaiseCanExecuteChanged(); // تحديث حالة الأزرار
            }
        }

        public string DriverContract
        {
            get => _driverContract;
            set => SetProperty(ref _driverContract, value);
        }

        public bool IsVisitNumberDuplicate
        {
            get => _isVisitNumberDuplicate;
            set => SetProperty(ref _isVisitNumberDuplicate, value);
        }

        public DateTime AddDate
        {
            get => _addDate;
            set
            {
                SetProperty(ref _addDate, value);
                UpdateHijriDate();
            }
        }

        public string HijriDate
        {
            get => _hijriDate;
            set => SetProperty(ref _hijriDate, value);
        }

        public DateTime DepartureDate
        {
            get => _departureDate;
            set
            {
                if (SetProperty(ref _departureDate, value))
                {
                    System.Diagnostics.Debug.WriteLine($"📅 تم تغيير تاريخ النزول إلى: {value:dd/MM/yyyy}");
                    CalculateDays();
                    RaisePropertyChanged(nameof(DaysCount)); // إضافة تحديث فوري
                }
            }
        }

        public DateTime ReturnDate
        {
            get => _returnDate;
            set
            {
                if (SetProperty(ref _returnDate, value))
                {
                    System.Diagnostics.Debug.WriteLine($"🏠 تم تغيير تاريخ العودة إلى: {value:dd/MM/yyyy}");
                    CalculateDays();
                    RaisePropertyChanged(nameof(DaysCount)); // إضافة تحديث فوري
                }
            }
        }

        public int DaysCount
        {
            get => _daysCount;
            set => SetProperty(ref _daysCount, value);
        }

        public string MissionPurpose
        {
            get => _missionPurpose;
            set
            {
                SetProperty(ref _missionPurpose, value);
                RaiseCanExecuteChanged(); // تحديث حالة الأزرار
            }
        }

        public Sector? SelectedSector
        {
            get => _selectedSector;
            set
            {
                SetProperty(ref _selectedSector, value);
                LoadSectorOfficers();
                RaiseCanExecuteChanged();
            }
        }

        public int VisitorsCount
        {
            get => _visitorsCount;
            set
            {
                SetProperty(ref _visitorsCount, value);
                UpdateVisitorInputs();
                RaiseCanExecuteChanged(); // تحديث حالة الأزرار
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string SaveButtonText
        {
            get => _saveButtonText;
            set => SetProperty(ref _saveButtonText, value);
        }

        public string CurrentDate
        {
            get => _currentDate;
            set => SetProperty(ref _currentDate, value);
        }

        public string CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        public int TotalParticipants
        {
            get => FieldVisits.Sum(v => v.VisitorsCount);
        }

        public int ActiveSectorsCount
        {
            get => FieldVisits.Select(v => v.SectorId).Distinct().Count();
        }

        public int ProjectsCount
        {
            get => _projectsCount;
            set
            {
                SetProperty(ref _projectsCount, value);
                UpdateProjectInputs(); // تحديث حقول المشاريع عند تغيير العدد
                ValidateProjectDays(); // التحقق من صحة أيام المشاريع
            }
        }

        public string SecurityRoute
        {
            get => _securityRoute;
            set => SetProperty(ref _securityRoute, value);
        }

        public string VisitNotes
        {
            get => _visitNotes;
            set => SetProperty(ref _visitNotes, value);
        }

        #endregion

        #region Commands

        public DelegateCommand AddCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand DeleteCommand { get; }
        public DelegateCommand IncreaseVisitorsCommand { get; }
        public DelegateCommand DecreaseVisitorsCommand { get; }
        public DelegateCommand ClearAllDataCommand { get; }
        public DelegateCommand IncreaseProjectsCommand { get; }
        public DelegateCommand DecreaseProjectsCommand { get; }
        public DelegateCommand AddNewProjectCommand { get; }
        public DelegateCommand<ProjectInput> SearchProjectCommand { get; }
        public DelegateCommand IncreaseItineraryCommand { get; }
        public DelegateCommand DecreaseItineraryCommand { get; }
        public DelegateCommand ImportExcelCommand { get; }
        public DelegateCommand ShowImportStatisticsCommand { get; }
        public DelegateCommand ShowSystemDashboardCommand { get; }
        public DelegateCommand ShowBatchImportCommand { get; }


        #endregion

        #region Command Implementations

        private async void AddFieldVisit()
        {
            // فحص التكرار قبل الإضافة
            if (IsVisitNumberDuplicate)
            {
                MessageBox.Show($"❌ رقم الزيارة '{VisitNumber}' موجود مسبقاً!\n\nيرجى اختيار رقم زيارة مختلف.", "رقم مكرر", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            // التحقق من البيانات الأساسية
            if (string.IsNullOrWhiteSpace(VisitNumber) || string.IsNullOrWhiteSpace(MissionPurpose) || SelectedSector == null)
            {
                MessageBox.Show("يرجى إكمال جميع البيانات المطلوبة:\n• رقم الزيارة\n• مهمة النزول\n• القطاع", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // التحقق من صحة بيانات المشاريع
            var projectValidation = ValidateProjectsData();
            if (!projectValidation.IsValid)
            {
                MessageBox.Show($"❌ خطأ في بيانات المشاريع:\n\n{projectValidation.ErrorMessage}", "خطأ في المشاريع", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }






            try
            {
                // إعادة حساب عدد الأيام قبل الحفظ لضمان الدقة
                CalculateDays();

                // تشخيص مفصل لخط السير قبل الحفظ
                System.Diagnostics.Debug.WriteLine("🔍 === تشخيص خط السير قبل الحفظ ===");
                System.Diagnostics.Debug.WriteLine($"🔍 عدد الأيام: {DaysCount}");
                System.Diagnostics.Debug.WriteLine($"🔍 عدد عناصر ItineraryDays: {ItineraryDays.Count}");

                for (int i = 0; i < ItineraryDays.Count; i++)
                {
                    var day = ItineraryDays[i];
                    System.Diagnostics.Debug.WriteLine($"🔍 اليوم {day.DayNumber}: '{day.Itinerary}' (فارغ: {string.IsNullOrWhiteSpace(day.Itinerary)})");
                }

                var itineraryList = ItineraryDays.Select(d => d.Itinerary).ToList();
                System.Diagnostics.Debug.WriteLine($"🔍 قائمة خط السير النهائية: {itineraryList.Count} عناصر");
                for (int i = 0; i < itineraryList.Count; i++)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 العنصر {i + 1}: '{itineraryList[i]}'");
                }
                System.Diagnostics.Debug.WriteLine("🔍 === نهاية التشخيص ===");

                var newVisit = new FieldVisit
                {
                    VisitNumber = VisitNumber,
                    DriverContract = DriverContract,
                    AddDate = AddDate,
                    HijriDate = HijriDate,
                    DepartureDate = DepartureDate,
                    ReturnDate = ReturnDate,
                    DaysCount = DaysCount,
                    MissionPurpose = MissionPurpose,
                    SectorId = SelectedSector?.Id ?? 0,
                    SectorName = SelectedSector?.Name ?? "",
                    VisitorsCount = VisitorsCount,
                    Visitors = VisitorInputs.Where(v => v.SelectedOfficer != null)
                                           .Select(v => new FieldVisitor
                    {
                        Name = v.SelectedOfficer?.Name ?? "",
                        OfficerId = v.SelectedOfficer?.Id ?? 0,
                        OfficerName = v.SelectedOfficer?.Name ?? "",
                        OfficerRank = v.SelectedOfficer?.Rank ?? "",
                        OfficerCode = v.SelectedOfficer?.Code ?? ""
                    }).ToList(),
                    Itinerary = itineraryList,
                    ProjectsCount = ProjectsCount,
                    Projects = await CreateFieldVisitProjectsAsync(ProjectInputs),
                    SecurityRoute = SecurityRoute,
                    VisitNotes = VisitNotes
                };

                // تشخيص إضافي للزيارة المنشأة
                System.Diagnostics.Debug.WriteLine($"🔍 الزيارة المنشأة - خط السير: {newVisit.Itinerary.Count} عناصر");
                for (int i = 0; i < newVisit.Itinerary.Count; i++)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 خط السير اليوم {i + 1}: '{newVisit.Itinerary[i]}'");
                }

                System.Diagnostics.Debug.WriteLine($"🔍 المشاريع المنشأة: {newVisit.Projects.Count} مشروع");
                for (int i = 0; i < newVisit.Projects.Count; i++)
                {
                    var project = newVisit.Projects[i];
                    System.Diagnostics.Debug.WriteLine($"🔍 المشروع {i + 1}: رقم='{project.ProjectNumber}', اسم='{project.ProjectName}'");
                }

                // تحديث نص زر الحفظ
                SaveButtonText = "💾 جاري الحفظ...";
                StatusMessage = "🔄 جاري حفظ البيانات...";

                var (success, errors) = await _dataService.AddFieldVisitAsync(newVisit);

                if (success)
                {
                    FieldVisits.Add(newVisit);
                    ClearForm();
                    GenerateVisitNumber();
                    GenerateDriverContract();
                    UpdateStatistics();
                    StatusMessage = $"✅ تم إضافة الزيارة {newVisit.VisitNumber} بنجاح";

                    // إشعار سجل الزيارات بالتحديث
                    NotifyFieldVisitsLogToRefresh();

                    // إظهار رسالة نجاح قوية
                    var successMessage = $"🎉 تم حفظ الزيارة بنجاح!\n\n" +
                                       $"🔢 رقم الزيارة: {newVisit.VisitNumber}\n" +
                                       $"🏢 القطاع: {newVisit.SectorName}\n" +
                                       $"👥 عدد المشاركين: {newVisit.VisitorsCount}\n" +
                                       $"📋 عدد المشاريع: {newVisit.Projects?.Count ?? 0}\n" +
                                       $"📅 تاريخ الإضافة: {newVisit.AddDate:yyyy/MM/dd - HH:mm}\n\n" +
                                       $"✨ تم حفظ جميع البيانات بنجاح في النظام!";

                    MessageBox.Show(successMessage, "🎯 نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    ShowValidationErrors(errors);
                }

                // إعادة تعيين نص زر الحفظ
                SaveButtonText = "💾 حفظ الزيارة";
            }
            catch (Exception ex)
            {
                var errorMessage = $"❌ حدث خطأ غير متوقع أثناء إضافة الزيارة:\n\n";
                errorMessage += $"نوع الخطأ: {ex.GetType().Name}\n";
                errorMessage += $"رسالة الخطأ: {ex.Message}\n";

                if (ex.InnerException != null)
                {
                    errorMessage += $"تفاصيل إضافية: {ex.InnerException.Message}\n";
                }

                errorMessage += "\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني";
                MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowValidationErrors(List<string> errors)
        {
            if (!errors.Any())
                return;

            var errorMessage = "❌ تم العثور على الأخطاء التالية:\n\n";

            for (int i = 0; i < errors.Count; i++)
            {
                errorMessage += $"{i + 1}. {errors[i]}\n";
            }

            errorMessage += "\n💡 يرجى تصحيح هذه الأخطاء وإعادة المحاولة";

            MessageBox.Show(errorMessage,
                           $"أخطاء في البيانات ({errors.Count} خطأ)",
                           MessageBoxButton.OK,
                           MessageBoxImage.Warning);
        }

        private async void SaveOrUpdateFieldVisit()
        {
            if (SelectedFieldVisit != null)
            {
                // تعديل زيارة موجودة
                await UpdateFieldVisit();
            }
            else
            {
                // إضافة زيارة جديدة
                AddFieldVisit();
            }
        }

        private async void SaveFieldVisit()
        {
            if (SelectedFieldVisit == null)
            {
                System.Diagnostics.Debug.WriteLine("❌ SaveFieldVisit: SelectedFieldVisit is null");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 SaveFieldVisit: بدء حفظ تعديلات الزيارة {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");

                // التحقق من صحة البيانات الأساسية قبل الحفظ
                if (SelectedSector == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم اختيار قطاع");
                    MessageBox.Show("❌ يجب اختيار قطاع للزيارة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(MissionPurpose))
                {
                    System.Diagnostics.Debug.WriteLine("❌ مهمة النزول فارغة");
                    MessageBox.Show("❌ مهمة النزول مطلوبة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // تحديث البيانات
                SelectedFieldVisit.AddDate = AddDate;
                SelectedFieldVisit.HijriDate = HijriDate;
                SelectedFieldVisit.DepartureDate = DepartureDate;
                SelectedFieldVisit.ReturnDate = ReturnDate;
                SelectedFieldVisit.DaysCount = DaysCount;
                SelectedFieldVisit.MissionPurpose = MissionPurpose;
                SelectedFieldVisit.SectorId = SelectedSector.Id;
                SelectedFieldVisit.SectorName = SelectedSector.Name;
                SelectedFieldVisit.VisitorsCount = VisitorsCount;
                SelectedFieldVisit.Visitors = VisitorInputs.Where(v => v.SelectedOfficer != null)
                                                          .Select(v => new FieldVisitor
                {
                    Name = v.SelectedOfficer?.Name ?? "",
                    OfficerId = v.SelectedOfficer?.Id ?? 0,
                    OfficerName = v.SelectedOfficer?.Name ?? "",
                    OfficerRank = v.SelectedOfficer?.Rank ?? "",
                    OfficerCode = v.SelectedOfficer?.Code ?? ""
                }).ToList();

                System.Diagnostics.Debug.WriteLine($"🔄 محاولة حفظ تعديلات الزيارة: {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");

                var success = await _dataService.UpdateFieldVisitAsync(SelectedFieldVisit);
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ التعديلات بنجاح للزيارة: {SelectedFieldVisit.VisitNumber}");
                    UpdateStatistics();
                    StatusMessage = $"تم تحديث الزيارة {SelectedFieldVisit.VisitNumber} بنجاح";
                    MessageBox.Show("✅ تم حفظ التعديلات بنجاح", "نجح العملية", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في حفظ التعديلات للزيارة: {SelectedFieldVisit.VisitNumber}");

                    // تشغيل التشخيص التلقائي عند فشل الحفظ
                    System.Diagnostics.Debug.WriteLine("🔍 بدء التشخيص التلقائي...");

                    try
                    {
                        var diagnosticHelper = new Helpers.FieldVisitDiagnosticHelper(_dataService);
                        var diagnosticResult = await diagnosticHelper.DiagnoseFieldVisitUpdateAsync(SelectedFieldVisit);

                        // بناء رسالة خطأ مفصلة مع نتائج التشخيص
                        var errorMessage = "❌ فشل في حفظ التعديلات\n\n";

                        if (diagnosticResult.Errors.Any())
                        {
                            errorMessage += "🔍 الأخطاء المكتشفة:\n";
                            foreach (var error in diagnosticResult.Errors)
                            {
                                errorMessage += $"• {error}\n";
                            }
                            errorMessage += "\n";
                        }

                        if (diagnosticResult.Warnings.Any())
                        {
                            errorMessage += "⚠️ تحذيرات:\n";
                            foreach (var warning in diagnosticResult.Warnings)
                            {
                                errorMessage += $"• {warning}\n";
                            }
                            errorMessage += "\n";
                        }

                        errorMessage += "💡 الحلول المقترحة:\n";
                        errorMessage += "• إعادة تشغيل البرنامج\n";
                        errorMessage += "• التحقق من صلاحيات قاعدة البيانات\n";
                        errorMessage += "• التأكد من صحة البيانات المدخلة\n";
                        errorMessage += "• الاتصال بالدعم الفني إذا استمرت المشكلة";

                        MessageBox.Show(errorMessage, "خطأ في الحفظ - تشخيص تلقائي", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    catch (Exception diagEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في التشخيص التلقائي: {diagEx.Message}");

                        var errorMessage = "❌ فشل في حفظ التعديلات\n\n";
                        errorMessage += "الأسباب المحتملة:\n";
                        errorMessage += "• خطأ في اتصال قاعدة البيانات\n";
                        errorMessage += "• تعارض في البيانات\n";
                        errorMessage += "• مشكلة في صلاحيات الملف\n\n";
                        errorMessage += "💡 يرجى المحاولة مرة أخرى أو إعادة تشغيل البرنامج";

                        MessageBox.Show(errorMessage, "خطأ في الحفظ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ استثناء في حفظ التعديلات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                var errorMessage = $"❌ خطأ في حفظ التعديلات:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                errorMessage += "\n\n💡 يرجى التحقق من اتصال قاعدة البيانات والمحاولة مرة أخرى";

                MessageBox.Show(errorMessage, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task UpdateFieldVisit()
        {
            if (SelectedFieldVisit == null)
            {
                System.Diagnostics.Debug.WriteLine("❌ UpdateFieldVisit: SelectedFieldVisit is null");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 UpdateFieldVisit: بدء تحديث الزيارة {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");

                // التحقق من صحة البيانات الأساسية قبل التحديث
                if (string.IsNullOrWhiteSpace(VisitNumber))
                {
                    System.Diagnostics.Debug.WriteLine("❌ رقم الزيارة فارغ");
                    MessageBox.Show("❌ رقم الزيارة مطلوب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (SelectedSector == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم اختيار قطاع");
                    MessageBox.Show("❌ يجب اختيار قطاع للزيارة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(MissionPurpose))
                {
                    System.Diagnostics.Debug.WriteLine("❌ مهمة النزول فارغة");
                    MessageBox.Show("❌ مهمة النزول مطلوبة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // إعادة حساب عدد الأيام قبل الحفظ لضمان الدقة
                CalculateDays();

                // تحديث البيانات الأساسية
                SelectedFieldVisit.VisitNumber = VisitNumber;
                SelectedFieldVisit.AddDate = AddDate;
                SelectedFieldVisit.HijriDate = HijriDate;
                SelectedFieldVisit.DepartureDate = DepartureDate;
                SelectedFieldVisit.ReturnDate = ReturnDate;
                SelectedFieldVisit.DaysCount = DaysCount;
                SelectedFieldVisit.MissionPurpose = MissionPurpose;
                SelectedFieldVisit.SectorId = SelectedSector.Id;
                SelectedFieldVisit.SectorName = SelectedSector.Name;
                SelectedFieldVisit.VisitorsCount = VisitorsCount;

                // تحديث الزوار
                SelectedFieldVisit.Visitors = VisitorInputs.Where(v => v.SelectedOfficer != null)
                                                          .Select(v => new FieldVisitor
                {
                    Name = v.SelectedOfficer?.Name ?? "",
                    OfficerId = v.SelectedOfficer?.Id ?? 0,
                    OfficerName = v.SelectedOfficer?.Name ?? "",
                    OfficerRank = v.SelectedOfficer?.Rank ?? "",
                    OfficerCode = v.SelectedOfficer?.Code ?? ""
                }).ToList();

                // تحديث خط السير
                SelectedFieldVisit.Itinerary = ItineraryDays.Select(d => d.Itinerary ?? "").ToList();

                // تحديث المشاريع
                SelectedFieldVisit.ProjectsCount = ProjectsCount;
                SelectedFieldVisit.Projects = await CreateFieldVisitProjectsAsync(ProjectInputs);

                // تحديث الحقول الإضافية
                SelectedFieldVisit.SecurityRoute = SecurityRoute;
                SelectedFieldVisit.VisitNotes = VisitNotes;

                System.Diagnostics.Debug.WriteLine($"🔄 محاولة تحديث الزيارة: {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");
                System.Diagnostics.Debug.WriteLine($"📊 بيانات الزيارة المحدثة:");
                System.Diagnostics.Debug.WriteLine($"   - القطاع: {SelectedFieldVisit.SectorName} (ID: {SelectedFieldVisit.SectorId})");
                System.Diagnostics.Debug.WriteLine($"   - عدد الزوار: {SelectedFieldVisit.VisitorsCount}");
                System.Diagnostics.Debug.WriteLine($"   - عدد المشاريع: {SelectedFieldVisit.ProjectsCount}");
                System.Diagnostics.Debug.WriteLine($"   - عدد عناصر خط السير: {SelectedFieldVisit.Itinerary.Count}");

                var success = await _dataService.UpdateFieldVisitAsync(SelectedFieldVisit);
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث الزيارة بنجاح: {SelectedFieldVisit.VisitNumber}");
                    UpdateStatistics();
                    StatusMessage = $"تم تحديث الزيارة {SelectedFieldVisit.VisitNumber} بنجاح";

                    // إشعار سجل الزيارات بالتحديث
                    NotifyFieldVisitsLogToRefresh();

                    MessageBox.Show("✅ تم حفظ التعديلات بنجاح", "نجح العملية", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في تحديث الزيارة: {SelectedFieldVisit.VisitNumber}");

                    // تشغيل التشخيص التلقائي عند فشل التحديث
                    System.Diagnostics.Debug.WriteLine("🔍 بدء التشخيص التلقائي...");

                    try
                    {
                        var diagnosticHelper = new Helpers.FieldVisitDiagnosticHelper(_dataService);
                        var diagnosticResult = await diagnosticHelper.DiagnoseFieldVisitUpdateAsync(SelectedFieldVisit);

                        // بناء رسالة خطأ مفصلة مع نتائج التشخيص
                        var errorMessage = "❌ فشل في حفظ التعديلات\n\n";

                        if (diagnosticResult.Errors.Any())
                        {
                            errorMessage += "🔍 الأخطاء المكتشفة:\n";
                            foreach (var error in diagnosticResult.Errors)
                            {
                                errorMessage += $"• {error}\n";
                            }
                            errorMessage += "\n";
                        }

                        if (diagnosticResult.Warnings.Any())
                        {
                            errorMessage += "⚠️ تحذيرات:\n";
                            foreach (var warning in diagnosticResult.Warnings)
                            {
                                errorMessage += $"• {warning}\n";
                            }
                            errorMessage += "\n";
                        }

                        errorMessage += "💡 الحلول المقترحة:\n";
                        errorMessage += "• إعادة تشغيل البرنامج\n";
                        errorMessage += "• التحقق من صلاحيات قاعدة البيانات\n";
                        errorMessage += "• التأكد من صحة البيانات المدخلة\n";
                        errorMessage += "• الاتصال بالدعم الفني إذا استمرت المشكلة";

                        MessageBox.Show(errorMessage, "خطأ في الحفظ - تشخيص تلقائي", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    catch (Exception diagEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في التشخيص التلقائي: {diagEx.Message}");

                        var errorMessage = "❌ فشل في حفظ التعديلات\n\n";
                        errorMessage += "الأسباب المحتملة:\n";
                        errorMessage += "• خطأ في اتصال قاعدة البيانات\n";
                        errorMessage += "• تعارض في البيانات\n";
                        errorMessage += "• مشكلة في صلاحيات الملف\n";
                        errorMessage += "• خطأ في حفظ المشاريع أو خط السير\n\n";
                        errorMessage += "💡 يرجى المحاولة مرة أخرى أو إعادة تشغيل البرنامج";

                        MessageBox.Show(errorMessage, "خطأ في الحفظ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ استثناء في تحديث الزيارة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                var errorMessage = $"❌ خطأ في حفظ التعديلات:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                errorMessage += "\n\n💡 يرجى التحقق من اتصال قاعدة البيانات والمحاولة مرة أخرى";

                MessageBox.Show(errorMessage, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteFieldVisit()
        {
            if (SelectedFieldVisit == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الزيارة رقم '{SelectedFieldVisit.VisitNumber}'؟",
                                       "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var visitToDelete = SelectedFieldVisit;
                    var success = await _dataService.DeleteFieldVisitAsync(visitToDelete.Id);
                    if (success)
                    {
                        var deletedVisitNumber = visitToDelete.VisitNumber;

                        // إزالة من القائمة المحلية
                        FieldVisits.Remove(visitToDelete);

                        // مسح النموذج
                        ClearForm();
                        GenerateVisitNumber();

                        // إعادة تحميل البيانات للتأكد من الحذف النهائي
                        await RefreshDataAsync();

                        UpdateStatistics();
                        StatusMessage = $"تم حذف الزيارة {deletedVisitNumber} بنجاح ونهائياً";
                    }
                    else
                    {
                        MessageBox.Show("❌ فشل في حذف الزيارة الميدانية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في حذف الزيارة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private bool CanExecuteAdd()
        {
            var canAdd = !string.IsNullOrWhiteSpace(VisitNumber) &&
                        !IsVisitNumberDuplicate &&
                        !string.IsNullOrWhiteSpace(MissionPurpose) &&
                        SelectedSector != null &&
                        VisitorsCount > 0;

            System.Diagnostics.Debug.WriteLine($"CanExecuteAdd: {canAdd} - VisitNumber: '{VisitNumber}', IsDuplicate: {IsVisitNumberDuplicate}, MissionPurpose: '{MissionPurpose}', SelectedSector: {SelectedSector?.Name}, VisitorsCount: {VisitorsCount}");

            return canAdd;
        }

        private bool CanExecuteSaveOrUpdate()
        {
            return CanExecuteAdd(); // يمكن الحفظ سواء كان إضافة أو تعديل
        }

        private bool CanExecuteSave()
        {
            return SelectedFieldVisit != null && CanExecuteAdd();
        }

        private bool CanExecuteDelete()
        {
            return SelectedFieldVisit != null;
        }

        private void IncreaseVisitors()
        {
            if (VisitorsCount < 20) // حد أقصى 20 مشارك
            {
                VisitorsCount++;
            }
        }

        private void DecreaseVisitors()
        {
            if (VisitorsCount > 1)
            {
                VisitorsCount--;
            }
        }

        private bool CanDecreaseVisitors()
        {
            return VisitorsCount > 1;
        }

        private async void ClearAllData()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من تفريغ جميع بيانات الزيارات الميدانية؟",
                "تأكيد تفريغ البيانات",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var success = await _dataService.ClearAllFieldVisitsAsync();
                    if (success)
                    {
                        FieldVisits.Clear();
                        ClearForm();
                        GenerateVisitNumber();
                        UpdateStatistics();
                        StatusMessage = "✅ تم تفريغ جميع بيانات الزيارات الميدانية بنجاح";
                    }
                    else
                    {
                        MessageBox.Show("❌ فشل في تفريغ البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في تفريغ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }



        private void IncreaseProjects()
        {
            ProjectsCount++;
        }

        private void DecreaseProjects()
        {
            if (ProjectsCount > 0)
            {
                ProjectsCount--;
            }
        }

        private bool CanDecreaseProjects()
        {
            return ProjectsCount > 0;
        }

        private void IncreaseItinerary()
        {
            var newDay = new ItineraryDay
            {
                DayNumber = ItineraryDays.Count + 1,
                Itinerary = string.Empty
            };
            ItineraryDays.Add(newDay);
            System.Diagnostics.Debug.WriteLine($"🗺️ تم إضافة يوم جديد لخط السير - العدد الحالي: {ItineraryDays.Count}");
        }

        private void DecreaseItinerary()
        {
            if (ItineraryDays.Count > 1)
            {
                ItineraryDays.RemoveAt(ItineraryDays.Count - 1);
                System.Diagnostics.Debug.WriteLine($"🗺️ تم حذف يوم من خط السير - العدد الحالي: {ItineraryDays.Count}");
            }
        }

        private bool CanDecreaseItinerary()
        {
            return ItineraryDays.Count > 1;
        }

        private async void ImportExcelFile()
        {
            var startTime = DateTime.Now;
            var importLogService = new Services.ImportLogService();
            string selectedFileName = "";

            try
            {
                // التحقق من وجود رقم مرجعي
                if (string.IsNullOrWhiteSpace(NumericInput))
                {
                    MessageBox.Show("⚠️ يجب إدخال الرقم المرجعي أولاً قبل استيراد ملف Excel",
                                  "رقم مرجعي مطلوب", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                StatusMessage = "جاري فتح مربع حوار اختيار الملف...";

                // استخدام خدمة حفظ آخر مجلد
                var folderMemoryService = new Services.FolderMemoryService();
                var lastFolder = folderMemoryService.GetLastFolder();

                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملف Excel للاستيراد",
                    Filter = "ملفات Excel (*.xlsx;*.xls)|*.xlsx;*.xls|جميع الملفات (*.*)|*.*",
                    FilterIndex = 1,
                    InitialDirectory = lastFolder,
                    RestoreDirectory = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    selectedFileName = System.IO.Path.GetFileName(openFileDialog.FileName);
                    StatusMessage = "جاري استيراد البيانات من ملف Excel...";

                    // حفظ آخر مجلد
                    var selectedFolder = folderMemoryService.GetFolderFromPath(openFileDialog.FileName);
                    folderMemoryService.SaveLastFolder(selectedFolder);

                    // استخدام خدمة استيراد Excel الفعلية
                    var excelImportService = new Services.ExcelImportService();
                    var importResult = await excelImportService.ImportFieldVisitFromExcel(openFileDialog.FileName, NumericInput);

                    if (importResult.VisitData != null)
                    {
                        // عرض نافذة التحقق من صحة البيانات أولاً
                        if (importResult.ValidationResult != null)
                        {
                            var validationWindow = new Views.ValidationResultWindow(importResult.ValidationResult);
                            var validationResult = validationWindow.ShowDialog();

                            if (validationResult != true || validationWindow.UserAction != Views.ValidationAction.Continue)
                            {
                                StatusMessage = "تم إلغاء الاستيراد بسبب مشاكل في البيانات";
                                return;
                            }
                        }

                        // إذا نجح التحقق، عرض نافذة المعاينة
                        var previewWindow = new Views.ExcelPreviewWindow(importResult);
                        var previewResult = previewWindow.ShowDialog();

                        if (previewResult == true && previewWindow.IsConfirmed)
                        {
                            // المستخدم أكد الاستيراد
                            await PopulateFormFromImportData(importResult);

                            // تسجيل عملية الاستيراد الناجحة
                            var processingTime = DateTime.Now - startTime;
                            var logEntry = new Services.ImportLogEntry
                            {
                                FileName = selectedFileName,
                                IndexValue = NumericInput,
                                VisitNumber = importResult.VisitData.VisitFormNumber,
                                ProjectsCount = importResult.Projects.Count,
                                ItineraryDaysCount = importResult.Itinerary.Count,
                                ProcessingTime = processingTime
                            };
                            await importLogService.LogSuccessfulImportAsync(logEntry);

                            MessageBox.Show($"✅ تم استيراد البيانات بنجاح من الملف:\n{selectedFileName}\n\n" +
                                          $"📋 البيانات المستوردة:\n" +
                                          $"• رقم الزيارة: {importResult.VisitData.VisitFormNumber}\n" +
                                          $"• عدد الأيام: {importResult.VisitData.FieldDaysCount}\n" +
                                          $"• المشاريع: {importResult.Projects.Count}\n" +
                                          $"• خط السير: {importResult.Itinerary.Count} أيام\n" +
                                          $"⏱️ وقت المعالجة: {processingTime.TotalSeconds:F1} ثانية",
                                          "نجح الاستيراد", MessageBoxButton.OK, MessageBoxImage.Information);

                            StatusMessage = "✅ تم استيراد البيانات من Excel وتعبئة الحقول بنجاح";
                        }
                        else
                        {
                            StatusMessage = "تم إلغاء استيراد البيانات من قبل المستخدم";
                        }
                    }
                    else
                    {
                        // تسجيل عملية الاستيراد الفاشلة
                        await importLogService.LogFailedImportAsync(selectedFileName, importResult.ErrorMessage, NumericInput);

                        MessageBox.Show($"❌ فشل في استيراد البيانات:\n{importResult.ErrorMessage}",
                                      "خطأ في الاستيراد", MessageBoxButton.OK, MessageBoxImage.Error);
                        StatusMessage = $"❌ فشل في الاستيراد: {importResult.ErrorMessage}";
                    }
                }
                else
                {
                    StatusMessage = "تم إلغاء عملية الاستيراد";
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ العام
                await importLogService.LogFailedImportAsync(selectedFileName, ex.Message, NumericInput);

                MessageBox.Show($"❌ خطأ في استيراد ملف Excel:\n{ex.Message}",
                              "خطأ في الاستيراد", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusMessage = "❌ فشل في استيراد ملف Excel";
            }
        }

        /// <summary>
        /// عرض نافذة إحصائيات الاستيراد
        /// </summary>
        private void ShowImportStatistics()
        {
            try
            {
                var statisticsWindow = new Views.ImportStatisticsWindow();
                statisticsWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine("✅ تم فتح نافذة إحصائيات الاستيراد");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فتح نافذة الإحصائيات: {ex.Message}");
                MessageBox.Show($"خطأ في فتح نافذة الإحصائيات:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض لوحة التحكم المتقدمة
        /// </summary>
        private void ShowSystemDashboard()
        {
            try
            {
                var dashboardWindow = new Views.SystemDashboardWindow();
                dashboardWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine("🚀 تم فتح لوحة التحكم المتقدمة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فتح لوحة التحكم: {ex.Message}");
                MessageBox.Show($"خطأ في فتح لوحة التحكم:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض نافذة الاستيراد المتعدد
        /// </summary>
        private void ShowBatchImport()
        {
            try
            {
                var batchImportWindow = new Views.BatchImportWindow();
                batchImportWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine("📂 تم فتح نافذة الاستيراد المتعدد");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فتح نافذة الاستيراد المتعدد: {ex.Message}");
                MessageBox.Show($"خطأ في فتح نافذة الاستيراد المتعدد:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// تعبئة النموذج من البيانات المستوردة من Excel
        /// </summary>
        private async Task PopulateFormFromImportData(Services.FieldVisitImportResult importResult)
        {
            try
            {
                var visitData = importResult.VisitData;
                if (visitData == null) return;

                System.Diagnostics.Debug.WriteLine("🔄 بدء تعبئة النموذج من البيانات المستوردة...");

                // تعبئة البيانات الأساسية
                VisitNumber = visitData.VisitFormNumber;
                DaysCount = visitData.FieldDaysCount;
                MissionPurpose = visitData.TripPurpose;

                // تعبئة التواريخ
                if (visitData.StartDate.HasValue)
                    DepartureDate = visitData.StartDate.Value;
                if (visitData.EndDate.HasValue)
                    ReturnDate = visitData.EndDate.Value;

                // البحث عن القطاع بالكود
                if (!string.IsNullOrWhiteSpace(visitData.Sector))
                {
                    var sector = await _dataService.GetSectorByCodeAsync(visitData.Sector);
                    if (sector != null)
                    {
                        SelectedSector = Sectors.FirstOrDefault(s => s.Id == sector.Id);
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على القطاع: {sector.Name}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على قطاع بالكود: {visitData.Sector}");
                    }
                }

                // تعبئة القائمين بالزيارة
                if (!string.IsNullOrWhiteSpace(visitData.VisitorCodes))
                {
                    // تحليل أكواد القائمين بالزيارة وتعبئة القائمة المنسدلة
                    await ProcessVisitorCodesForFormAsync(visitData.VisitorCodes);
                }
                else if (!string.IsNullOrWhiteSpace(visitData.Visitors))
                {
                    // محاولة استخراج عدد الزوار من النص (للتوافق مع النسخ القديمة)
                    if (int.TryParse(visitData.Visitors, out int visitorsCount))
                    {
                        VisitorsCount = Math.Max(1, visitorsCount);
                    }
                    else
                    {
                        // إذا كان النص يحتوي على أسماء، احسب عدد الأسماء
                        var names = visitData.Visitors.Split(new[] { " - " }, StringSplitOptions.RemoveEmptyEntries);
                        VisitorsCount = Math.Max(1, names.Length);
                    }
                }

                // تعبئة المشاريع
                if (importResult.Projects.Any())
                {
                    ProjectsCount = Math.Min(importResult.Projects.Count, 5); // الحد الأقصى 5 مشاريع
                    UpdateProjectInputs();

                    for (int i = 0; i < Math.Min(importResult.Projects.Count, ProjectInputs.Count); i++)
                    {
                        var importedProject = importResult.Projects[i];
                        var projectInput = ProjectInputs[i];

                        projectInput.ProjectNumber = importedProject.ProjectCode;
                        projectInput.ProjectName = importedProject.ProjectName;
                        projectInput.ProjectDays = importedProject.ProjectDays;
                        projectInput.IsProjectFound = !string.IsNullOrWhiteSpace(importedProject.ProjectName);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تعبئة {importResult.Projects.Count} مشروع");
                }

                // تعبئة خط السير
                if (importResult.Itinerary.Any())
                {
                    // تحديث عدد أيام خط السير
                    UpdateItineraryDays();

                    for (int i = 0; i < Math.Min(importResult.Itinerary.Count, ItineraryDays.Count); i++)
                    {
                        ItineraryDays[i].Itinerary = importResult.Itinerary[i].Plan;
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ تم تعبئة خط السير لـ {importResult.Itinerary.Count} أيام");
                }

                // تعبئة خط السير للنقاط الأمنية
                if (!string.IsNullOrWhiteSpace(visitData.SecurityRoute))
                {
                    SecurityRoute = visitData.SecurityRoute;
                    System.Diagnostics.Debug.WriteLine($"✅ تم تعبئة خط السير للنقاط الأمنية: {visitData.SecurityRoute}");
                }

                // تعبئة ملاحظات الزيارة
                if (!string.IsNullOrWhiteSpace(visitData.VisitNotes))
                {
                    VisitNotes = visitData.VisitNotes;
                    System.Diagnostics.Debug.WriteLine($"✅ تم تعبئة ملاحظات الزيارة: {visitData.VisitNotes}");
                }

                // تحديث التاريخ الهجري
                UpdateHijriDate();

                System.Diagnostics.Debug.WriteLine("✅ تم تعبئة النموذج بنجاح من البيانات المستوردة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تعبئة النموذج: {ex.Message}");
                MessageBox.Show($"❌ خطأ في تعبئة النموذج من البيانات المستوردة:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddNewProject()
        {
            try
            {
                var addProjectWindow = new Views.AddProjectWindow();

                var result = addProjectWindow.ShowDialog();

                if (result == true)
                {
                    // إعادة تحميل المشاريع بعد الإضافة
                    await RefreshProjectsAsync();
                    StatusMessage = "✅ تم إضافة المشروع بنجاح وتحديث القائمة";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في فتح نافذة إضافة المشروع: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إشعار سجل الزيارات الميدانية بالتحديث - نسخة محسنة
        /// </summary>
        private async void NotifyFieldVisitsLogToRefresh()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إشعار سجل الزيارات بالتحديث...");

                // استخدام Dispatcher للتأكد من التحديث في UI Thread
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    // البحث عن MainWindow
                    var mainWindow = Application.Current.MainWindow as MainWindow;
                    if (mainWindow?.DataContext is MainViewModel mainViewModel)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ تم العثور على MainWindow");

                        // إشعار عام لجميع ViewModels بالتحديث
                        FieldVisitsUpdated?.Invoke();

                        // تحديث مباشر إذا كان المستخدم في صفحة سجل الزيارات
                        if (mainViewModel.CurrentView is Views.FieldVisitsLogView logView &&
                            logView.DataContext is FieldVisitsLogViewModel logViewModel)
                        {
                            System.Diagnostics.Debug.WriteLine("✅ المستخدم في صفحة سجل الزيارات - تحديث مباشر");
                            await Task.Delay(500); // انتظار قصير للتأكد من حفظ البيانات
                            logViewModel.RefreshDataCommand.Execute();
                        }

                        System.Diagnostics.Debug.WriteLine("✅ تم إشعار سجل الزيارات بالتحديث بنجاح");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على MainWindow أو MainViewModel");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إشعار سجل الزيارات: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث لإشعار ViewModels أخرى بتحديث الزيارات
        /// </summary>
        public static event Action? FieldVisitsUpdated;

        /// <summary>
        /// حدث لطلب تعديل زيارة من صفحات أخرى
        /// </summary>
        public static event Action<FieldVisit>? EditVisitRequested;

        /// <summary>
        /// طريقة عامة لطلب تعديل زيارة
        /// </summary>
        public static void RequestEditVisit(FieldVisit visit)
        {
            EditVisitRequested?.Invoke(visit);
        }

        /// <summary>
        /// معالج حدث طلب تعديل زيارة
        /// </summary>
        private async void OnEditVisitRequested(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 OnEditVisitRequested: تم استلام طلب تعديل الزيارة {visit.VisitNumber}");

                // Use dispatcher to ensure we're on UI thread
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        // Clear any existing data first
                        ClearForm();

                        // Wait a moment for the form to clear
                        await Task.Delay(100);

                        // Set the visit for editing - this will trigger LoadFieldVisitDetailsAsync
                        SelectedFieldVisit = visit;

                        System.Diagnostics.Debug.WriteLine($"✅ OnEditVisitRequested: تم تعيين الزيارة للتعديل - {visit.VisitNumber}");

                        // Show success message
                        StatusMessage = $"تم تحميل الزيارة {visit.VisitNumber} للتعديل";

                        // Show confirmation to user
                        MessageBox.Show($"✅ تم تحميل الزيارة {visit.VisitNumber} للتعديل بنجاح\n\nيمكنك الآن تعديل البيانات وحفظها",
                                      "تم التحميل", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception innerEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ OnEditVisitRequested Inner Error: {innerEx.Message}");
                        StatusMessage = $"خطأ في تحميل الزيارة للتعديل: {innerEx.Message}";
                        MessageBox.Show($"❌ خطأ في تحميل الزيارة للتعديل:\n{innerEx.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ OnEditVisitRequested Error: {ex.Message}");
                StatusMessage = $"خطأ في تحميل الزيارة للتعديل: {ex.Message}";
                MessageBox.Show($"❌ خطأ في تحميل الزيارة للتعديل:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة أكواد القائمين بالزيارة وتعبئة النموذج
        /// </summary>
        private async Task ProcessVisitorCodesForFormAsync(string visitorCodes)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(visitorCodes))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد أكواد قائمين بالزيارة");
                    return;
                }

                // تقسيم الأكواد (مفصولة بمسافات)
                var codes = visitorCodes.Split(new[] { ' ', '\t', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                       .Select(c => c.Trim())
                                       .Where(c => !string.IsNullOrEmpty(c))
                                       .ToList();

                System.Diagnostics.Debug.WriteLine($"🔍 أكواد القائمين بالزيارة: {string.Join(", ", codes)}");

                if (codes.Count == 0)
                {
                    return;
                }

                // تحديد عدد الزوار
                VisitorsCount = codes.Count;
                UpdateVisitorInputs();

                // البحث عن الموظفين بالأكواد
                using var context = new ApplicationDbContext();
                var officers = await context.Officers
                                           .Where(o => codes.Contains(o.Code) && o.IsActive)
                                           .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {officers.Count} موظف من أصل {codes.Count}");

                // تحديد القطاع أولاً من أول موظف موجود
                var firstOfficer = officers.FirstOrDefault();
                if (firstOfficer != null && SelectedSector == null && firstOfficer.SectorId > 0)
                {
                    SelectedSector = Sectors.FirstOrDefault(s => s.Id == firstOfficer.SectorId);
                    if (SelectedSector != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"🎯 تم تحديد القطاع: {SelectedSector.Name}");
                        await UpdateSectorOfficersAsync();
                        System.Diagnostics.Debug.WriteLine($"📋 تم تحديث قائمة موظفي القطاع: {SectorOfficers.Count} موظف");
                    }
                }

                // تعبئة القائمة المنسدلة
                for (int i = 0; i < codes.Count && i < VisitorInputs.Count; i++)
                {
                    var code = codes[i];
                    var officer = officers.FirstOrDefault(o => o.Code == code);

                    if (officer != null)
                    {
                        // البحث عن الموظف في قائمة موظفي القطاع المحدثة
                        var matchingOfficer = SectorOfficers.FirstOrDefault(o => o.Id == officer.Id);
                        if (matchingOfficer != null)
                        {
                            VisitorInputs[i].SelectedOfficer = matchingOfficer;
                            System.Diagnostics.Debug.WriteLine($"✅ تم تعيين الكود {code} -> {officer.Name}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ الموظف {officer.Name} غير موجود في قائمة موظفي القطاع");

                            // إذا لم يوجد في القائمة، أضفه مؤقتاً
                            SectorOfficers.Add(officer);
                            VisitorInputs[i].SelectedOfficer = officer;
                            System.Diagnostics.Debug.WriteLine($"🔄 تم إضافة الموظف مؤقتاً: {officer.Name}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ الكود {code} غير موجود في قاعدة البيانات");
                    }
                }

                // إشعار التحديث للواجهة
                RaisePropertyChanged(nameof(SectorOfficers));
                RaisePropertyChanged(nameof(VisitorInputs));
                System.Diagnostics.Debug.WriteLine($"🔄 تم إشعار الواجهة بالتحديث");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة أكواد القائمين بالزيارة: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private void CalculateDays()
        {
            // خذ التواريخ بدون الوقت لتجنب الفروقات بسبب الساعة
            DateTime start = DepartureDate.Date;
            DateTime end = ReturnDate.Date;

            int newDaysCount;

            // إذا كان تاريخ العودة قبل تاريخ النزول
            if (end < start)
            {
                newDaysCount = 0;
                System.Diagnostics.Debug.WriteLine("⚠️ تاريخ العودة أقل من تاريخ النزول - عدد الأيام = 0");
            }
            else
            {
                // أضف +1 لتحسب اليوم نفسه شاملة (مثال: من 1 إلى 1 = 1 يوم)
                newDaysCount = (end - start).Days + 1;

                System.Diagnostics.Debug.WriteLine($"🔢 حساب الأيام: من {start:dd/MM/yyyy} إلى {end:dd/MM/yyyy} = {newDaysCount} أيام");
            }

            // تحديث القيمة مع إشعار التغيير
            if (_daysCount != newDaysCount)
            {
                _daysCount = newDaysCount;
                RaisePropertyChanged(nameof(DaysCount));
                // خط السير مستقل تماماً عن عدد الأيام
                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث عدد الأيام إلى: {newDaysCount} (خط السير مستقل)");
            }
        }

        private async void LoadData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting LoadData...");
                StatusMessage = "جاري تحميل البيانات...";

                // Load sectors
                System.Diagnostics.Debug.WriteLine("Loading sectors...");
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }
                System.Diagnostics.Debug.WriteLine($"Loaded {Sectors.Count} sectors");

                // Load projects
                System.Diagnostics.Debug.WriteLine("Loading projects...");
                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }
                System.Diagnostics.Debug.WriteLine($"Loaded {Projects.Count} projects");

                // Load field visits
                System.Diagnostics.Debug.WriteLine("Loading field visits...");
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits)
                {
                    FieldVisits.Add(visit);
                }
                System.Diagnostics.Debug.WriteLine($"Loaded {FieldVisits.Count} field visits");

                UpdateStatistics();
                CheckVisitNumberDuplicate(); // فحص التكرار بعد تحميل البيانات
                StatusMessage = $"تم تحميل {FieldVisits.Count} زيارة ميدانية";
                System.Diagnostics.Debug.WriteLine("LoadData completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadData: {ex.Message}");
                StatusMessage = "خطأ في تحميل البيانات";

                // Don't show MessageBox during initialization as it might cause issues
                // MessageBox.Show($"❌ خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Refreshing data...");

                // إعادة تحميل الزيارات الميدانية من قاعدة البيانات
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits)
                {
                    FieldVisits.Add(visit);
                }

                System.Diagnostics.Debug.WriteLine($"Refreshed {FieldVisits.Count} field visits");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing data: {ex.Message}");
            }
        }

        private async Task RefreshProjectsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Refreshing projects...");

                // إعادة تحميل المشاريع من قاعدة البيانات
                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }

                System.Diagnostics.Debug.WriteLine($"Refreshed {Projects.Count} projects");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing projects: {ex.Message}");
            }
        }

        private void GenerateVisitNumber()
        {
            // لا نحتاج لتوليد رقم تلقائي بعد الآن - المستخدم سيدخله يدوياً
            VisitNumber = string.Empty;
        }

        private void CheckVisitNumberDuplicate()
        {
            if (string.IsNullOrWhiteSpace(VisitNumber))
            {
                IsVisitNumberDuplicate = false;
                return;
            }

            // فحص التكرار مع استثناء الزيارة الحالية في حالة التعديل
            var isDuplicate = FieldVisits.Any(v => v.VisitNumber.Equals(VisitNumber, StringComparison.OrdinalIgnoreCase)
                                                  && (SelectedFieldVisit == null || v.Id != SelectedFieldVisit.Id));
            IsVisitNumberDuplicate = isDuplicate;
        }

        private void UpdateHijriDate()
        {
            try
            {
                var hijriCalendar = new System.Globalization.HijriCalendar();
                var hijriYear = hijriCalendar.GetYear(AddDate);
                var hijriMonth = hijriCalendar.GetMonth(AddDate);
                var hijriDay = hijriCalendar.GetDayOfMonth(AddDate);

                string[] hijriMonthNames = {
                    "محرم", "صفر", "ربيع الأول", "ربيع الآخر", "جمادى الأولى", "جمادى الآخرة",
                    "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
                };

                var monthName = hijriMonth <= hijriMonthNames.Length ? hijriMonthNames[hijriMonth - 1] : hijriMonth.ToString();
                HijriDate = $"{hijriDay} {monthName} {hijriYear} هـ";
            }
            catch
            {
                HijriDate = "غير متاح";
            }
        }

        private async void LoadSectorOfficers()
        {
            await UpdateSectorOfficersAsync();
        }

        private async Task UpdateSectorOfficersAsync()
        {
            if (SelectedSector == null)
            {
                SectorOfficers.Clear();
                return;
            }



            try
            {
                var officers = await _dataService.GetOfficersBySectorAsync(SelectedSector.Id);
                SectorOfficers.Clear();
                foreach (var officer in officers)
                {
                    SectorOfficers.Add(officer);
                }
                System.Diagnostics.Debug.WriteLine($"Updated sector officers: {SectorOfficers.Count} officers for sector {SelectedSector.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating sector officers: {ex.Message}");
            }
        }

        private void UpdateVisitorInputs()
        {
            VisitorInputs.Clear();
            for (int i = 1; i <= VisitorsCount; i++)
            {
                VisitorInputs.Add(new VisitorInput { Number = $"{i}." });
            }
        }

        private void UpdateItineraryDays()
        {
            // الاحتفاظ بالبيانات الموجودة
            var existingItinerary = ItineraryDays.ToDictionary(d => d.DayNumber, d => d.Itinerary);

            ItineraryDays.Clear();
            for (int i = 1; i <= DaysCount; i++)
            {
                var itineraryDay = new ItineraryDay
                {
                    DayNumber = i,
                    Itinerary = existingItinerary.ContainsKey(i) ? existingItinerary[i] : string.Empty
                };
                ItineraryDays.Add(itineraryDay);
            }

            System.Diagnostics.Debug.WriteLine($"Updated itinerary days: {ItineraryDays.Count} days for {DaysCount} total days");
        }

        private void UpdateProjectInputs()
        {
            // الاحتفاظ بالبيانات الموجودة
            var existingProjects = ProjectInputs.ToDictionary(p => int.Parse(p.Number.TrimEnd('.')), p => p);

            ProjectInputs.Clear();
            for (int i = 1; i <= ProjectsCount; i++)
            {
                var projectInput = new ProjectInput
                {
                    Number = $"{i}."
                };

                // استرجاع البيانات الموجودة إن وجدت
                if (existingProjects.ContainsKey(i))
                {
                    var existing = existingProjects[i];
                    projectInput.ProjectNumber = existing.ProjectNumber;
                    projectInput.ProjectName = existing.ProjectName;
                    projectInput.ProjectDays = existing.ProjectDays;
                    projectInput.IsProjectFound = existing.IsProjectFound;
                }

                ProjectInputs.Add(projectInput);
            }

            System.Diagnostics.Debug.WriteLine($"Updated project inputs: {ProjectInputs.Count} projects for {ProjectsCount} total projects");
        }

        private async void SearchProjectByNumber(ProjectInput projectInput)
        {
            if (string.IsNullOrWhiteSpace(projectInput.ProjectNumber))
            {
                projectInput.ProjectName = string.Empty;
                projectInput.IsProjectFound = true;
                return;
            }



            try
            {
                var project = await _dataService.GetProjectByNumberAsync(projectInput.ProjectNumber);
                if (project != null)
                {
                    projectInput.ProjectName = project.ProjectName;
                    projectInput.IsProjectFound = true;
                }
                else
                {
                    projectInput.ProjectName = "المشروع غير موجود";
                    projectInput.IsProjectFound = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error searching project: {ex.Message}");
                projectInput.ProjectName = "خطأ في البحث";
                projectInput.IsProjectFound = false;
            }
        }

        private void ValidateProjectDays()
        {
            // التحقق من أن مجموع أيام المشاريع = أيام الزيارة
            var totalProjectDays = ProjectInputs.Sum(p => p.ProjectDays);

            if (totalProjectDays != DaysCount && totalProjectDays > 0)
            {
                System.Diagnostics.Debug.WriteLine($"Project days validation failed: Total project days ({totalProjectDays}) != Visit days ({DaysCount})");
            }
        }

        private async Task LoadFieldVisitDetailsAsync()
        {
            if (SelectedFieldVisit == null) return;



            try
            {
                VisitNumber = SelectedFieldVisit.VisitNumber;
                AddDate = SelectedFieldVisit.AddDate;
                HijriDate = SelectedFieldVisit.HijriDate;
                DepartureDate = SelectedFieldVisit.DepartureDate;
                ReturnDate = SelectedFieldVisit.ReturnDate;

                // إعادة حساب عدد الأيام بدلاً من استخدام القيمة المحفوظة
                CalculateDays();

                MissionPurpose = SelectedFieldVisit.MissionPurpose;
                VisitorsCount = SelectedFieldVisit.VisitorsCount;

                // Set selected sector first
                SelectedSector = Sectors.FirstOrDefault(s => s.Id == SelectedFieldVisit.SectorId);

                // Update sector officers after setting sector and WAIT for completion
                await UpdateSectorOfficersAsync();

                // Load visitors after updating officers
                VisitorInputs.Clear();
                for (int i = 0; i < SelectedFieldVisit.Visitors.Count; i++)
                {
                    var visitor = SelectedFieldVisit.Visitors[i];
                    var visitorInput = new VisitorInput
                    {
                        Number = $"{i + 1}.",
                        SelectedOfficer = SectorOfficers.FirstOrDefault(o => o.Id == visitor.OfficerId)
                    };
                    VisitorInputs.Add(visitorInput);
                }

                // إضافة مدخلات فارغة إذا كان عدد المشاركين أكبر من عدد الزوار المحفوظين
                while (VisitorInputs.Count < VisitorsCount)
                {
                    VisitorInputs.Add(new VisitorInput { Number = $"{VisitorInputs.Count + 1}." });
                }

                // Load itinerary
                ItineraryDays.Clear();
                for (int i = 1; i <= DaysCount; i++)
                {
                    var itinerary = i <= SelectedFieldVisit.Itinerary.Count ? SelectedFieldVisit.Itinerary[i - 1] : string.Empty;
                    ItineraryDays.Add(new ItineraryDay
                    {
                        DayNumber = i,
                        Itinerary = itinerary
                    });
                }

                // Load projects
                ProjectsCount = SelectedFieldVisit.Projects?.Count ?? 0;
                ProjectInputs.Clear();

                if (SelectedFieldVisit.Projects != null && SelectedFieldVisit.Projects.Any())
                {
                    for (int i = 0; i < SelectedFieldVisit.Projects.Count; i++)
                    {
                        var project = SelectedFieldVisit.Projects[i];
                        var projectInput = new ProjectInput
                        {
                            Number = $"{i + 1}.",
                            ProjectNumber = project.ProjectNumber ?? string.Empty,
                            ProjectName = project.ProjectName ?? string.Empty
                        };
                        ProjectInputs.Add(projectInput);
                    }
                }

                // إضافة مدخلات فارغة إذا كان عدد المشاريع أكبر من المشاريع المحفوظة
                while (ProjectInputs.Count < ProjectsCount)
                {
                    ProjectInputs.Add(new ProjectInput { Number = $"{ProjectInputs.Count + 1}." });
                }

                // Load new fields
                SecurityRoute = SelectedFieldVisit.SecurityRoute ?? string.Empty;
                VisitNotes = SelectedFieldVisit.VisitNotes ?? string.Empty;

                // Update button text
                UpdateButtonText();

                System.Diagnostics.Debug.WriteLine($"Loaded field visit details: {SelectedFieldVisit.VisitNumber}, Visitors: {VisitorInputs.Count}, Officers: {SectorOfficers.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading field visit details: {ex.Message}");
            }
        }

        private void UpdateButtonText()
        {
            SaveButtonText = SelectedFieldVisit != null ? "تعديل الزيارة" : "إضافة الزيارة";
        }

        public void ClearForm()
        {
            SelectedFieldVisit = null;
            NumericInput = string.Empty;
            AddDate = DateTime.Now;
            DepartureDate = DateTime.Now;
            ReturnDate = DateTime.Now;
            MissionPurpose = string.Empty;
            SelectedSector = null;
            VisitorsCount = 1;
            SecurityRoute = string.Empty;
            VisitNotes = string.Empty;

            UpdateHijriDate();
            CalculateDays();
            UpdateVisitorInputs();
            UpdateItineraryDays(); // Clear and update itinerary
            UpdateProjectInputs(); // Clear and update projects
            UpdateButtonText(); // Update button text when clearing form
        }

        private void RaiseCanExecuteChanged()
        {
            AddCommand.RaiseCanExecuteChanged();
            SaveCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
            DecreaseVisitorsCommand.RaiseCanExecuteChanged();
            DecreaseProjectsCommand.RaiseCanExecuteChanged();
            DecreaseItineraryCommand.RaiseCanExecuteChanged();
        }

        private void StartTimeUpdateTimer()
        {
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += (s, e) =>
            {
                CurrentTime = DateTime.Now.ToString("HH:mm:ss");
                CurrentDate = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
            };
            timer.Start();
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalParticipants));
            RaisePropertyChanged(nameof(ActiveSectorsCount));
        }

        private async void GenerateDriverContract()
        {
            try
            {
                // إنشاء رقم عقد السائق بالتنسيق المطلوب:
                // السنة الحالية (4 أرقام) + الشهر (2 رقم) + رقم تسلسلي
                var now = DateTime.Now;
                var year = now.Year.ToString(); // السنة كاملة (4 أرقام)
                var month = now.Month.ToString("D2"); // الشهر برقمين

                // الحصول على عدد العقود في نفس الشهر لتحديد الرقم التسلسلي
                var monthContractsCount = await GetMonthContractsCountAsync();
                var sequentialNumber = (monthContractsCount + 1).ToString(); // رقم تسلسلي

                // تكوين رقم العقد النهائي (مثال: 2025061)
                DriverContract = $"{year}{month}{sequentialNumber}";

                System.Diagnostics.Debug.WriteLine($"Generated driver contract: {DriverContract}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating driver contract: {ex.Message}");
                var now = DateTime.Now;
                DriverContract = $"{now:yyyyMM}1";
            }
        }

        private async Task<int> GetTodayVisitsCountAsync()
        {
            try
            {
                var today = DateTime.Today;
                var visits = await _dataService.GetFieldVisitsAsync();
                return visits.Count(v => v.AddDate.Date == today);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting today visits count: {ex.Message}");
                return 0;
            }
        }

        private async Task<int> GetMonthContractsCountAsync()
        {
            try
            {
                var fieldVisits = await _dataService.GetFieldVisitsAsync();
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                return fieldVisits.Count(v => v.AddDate.Month == currentMonth && v.AddDate.Year == currentYear);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting month's contracts count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// إنشاء مشاريع الزيارة مع ربطها بجدول المشاريع الأساسي
        /// </summary>
        private async Task<List<FieldVisitProject>> CreateFieldVisitProjectsAsync(ObservableCollection<ProjectInput> projectInputs)
        {
            var fieldVisitProjects = new List<FieldVisitProject>();

            try
            {
                // جلب جميع المشاريع من قاعدة البيانات
                var allProjects = await _dataService.GetProjectsAsync();

                var validProjects = projectInputs.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber)).ToList();

                for (int i = 0; i < validProjects.Count; i++)
                {
                    var projectInput = validProjects[i];

                    // البحث عن المشروع في قاعدة البيانات
                    var existingProject = allProjects.FirstOrDefault(p =>
                        p.ProjectNumber.Equals(projectInput.ProjectNumber, StringComparison.OrdinalIgnoreCase));

                    var fieldVisitProject = new FieldVisitProject
                    {
                        Id = 0, // كائن جديد
                        FieldVisitId = 0, // سيتم تحديثه بعد الحفظ
                        ProjectId = existingProject?.Id, // ربط بالمشروع الموجود إن وجد
                        ProjectNumber = projectInput.ProjectNumber,
                        ProjectName = projectInput.ProjectName,
                        DisplayOrder = i + 1,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    fieldVisitProjects.Add(fieldVisitProject);

                    System.Diagnostics.Debug.WriteLine($"🔍 المشروع {i + 1}: رقم='{projectInput.ProjectNumber}', " +
                        $"اسم='{projectInput.ProjectName}', مربوط={existingProject != null}");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {fieldVisitProjects.Count} مشروع للزيارة");
                return fieldVisitProjects;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مشاريع الزيارة: {ex.Message}");

                // في حالة الخطأ، إنشاء المشاريع بدون ربط
                return projectInputs.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber))
                                   .Select((p, index) => new FieldVisitProject
                                   {
                                       Id = 0,
                                       FieldVisitId = 0,
                                       ProjectId = null,
                                       ProjectNumber = p.ProjectNumber,
                                       ProjectName = p.ProjectName,
                                       DisplayOrder = index + 1,
                                       IsActive = true,
                                       CreatedAt = DateTime.Now
                                   }).ToList();
            }
        }

        #endregion

        private async void OnDatabaseRefreshed()
        {
            try
            {
                // Reload look‑ups from the shared data service
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var s in sectors) Sectors.Add(s);
                RaisePropertyChanged(nameof(Sectors));

                var officers = await _dataService.GetOfficersAsync();
                SectorOfficers.Clear();
                foreach (var o in officers) SectorOfficers.Add(o);
                RaisePropertyChanged(nameof(SectorOfficers));

                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var p in projects) Projects.Add(p);
                RaisePropertyChanged(nameof(Projects));

                StatusMessage = "✅ تم تحديث البيانات بنجاح";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reloading data: {ex.Message}");
            }
        }

        /// <summary>
        /// ������ �� ��� ������ ��������
        /// </summary>


        /// <summary>
        /// التحقق من صحة بيانات المشاريع
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateProjectsData()
        {
            try
            {
                // في النظام المبسط، نتحقق فقط من وجود بيانات المشاريع إذا تم إدخالها
                var projectsWithData = ProjectInputs.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber) || !string.IsNullOrWhiteSpace(p.ProjectName)).ToList();

                // إذا لم يتم إدخال أي مشاريع، فهذا مقبول
                if (!projectsWithData.Any())
                {
                    return (true, string.Empty);
                }

                foreach (var project in projectsWithData)
                {
                    if (string.IsNullOrWhiteSpace(project.ProjectNumber))
                    {
                        return (false, "يجب إدخال رقم المشروع لجميع المشاريع المدخلة");
                    }

                    if (string.IsNullOrWhiteSpace(project.ProjectName))
                    {
                        return (false, $"يجب إدخال اسم المشروع للمشروع رقم: {project.ProjectNumber}");
                    }
                }

                // التحقق من عدم تكرار أرقام المشاريع
                var projectNumbers = projectsWithData.Select(p => p.ProjectNumber).ToList();
                var duplicates = projectNumbers.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key).ToList();

                if (duplicates.Any())
                {
                    return (false, $"أرقام المشاريع التالية مكررة: {string.Join(", ", duplicates)}");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"خطأ في التحقق من بيانات المشاريع: {ex.Message}");
            }
        }

}
}








