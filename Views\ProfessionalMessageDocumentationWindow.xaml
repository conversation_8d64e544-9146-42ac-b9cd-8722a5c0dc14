<Window x:Class="DriverManagementSystem.Views.ProfessionalMessageDocumentationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
        Title="توثيق الرسائل النصية للنزول الميداني - النسخة الاحترافية"
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        WindowState="Maximized">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:FileNameConverter x:Key="FileNameConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📱" FontSize="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="توثيق الرسائل النصية للنزول الميداني - النسخة الاحترافية" 
                                 FontSize="20" FontWeight="Bold" Foreground="White"/>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="{Binding SelectedVisit.VisitNumber, StringFormat='رقم الزيارة: {0}'}" 
                                     FontSize="14" Foreground="LightGray" Margin="0,0,20,0"/>
                            <TextBlock Text="{Binding ContractNumber, StringFormat='رقم العقد: {0}'}" 
                                     FontSize="14" Foreground="LightGray"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding Documentation.DocumentationDate, StringFormat='dd/MM/yyyy'}" 
                             FontSize="16" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="3*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Panel - Form -->
                <Border Grid.Column="0" Background="White" Padding="20" CornerRadius="8" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="📋 بيانات التوثيق" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>

                        <!-- Visit and Contract Numbers -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="رقم الزيارة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding SelectedVisit.VisitNumber}" IsReadOnly="True" 
                                       Background="#F0F0F0" Padding="8" FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="رقم العقد:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding ContractNumber}" Padding="8" FontSize="14"/>
                            </StackPanel>
                        </Grid>

                        <!-- Report Number -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="رقم التقرير:" Grid.Column="0" VerticalAlignment="Center" FontWeight="Bold"/>
                            <TextBox Text="{Binding ReportNumber}" Grid.Column="1" Padding="8" FontSize="14"/>
                        </Grid>

                        <!-- Officers -->
                        <TextBlock Text="المختصين بالبترول:" FontSize="16" FontWeight="Bold" Margin="0,20,0,10"/>
                        
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="الأول:" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding FirstOfficer}" Grid.Column="1" Padding="8" FontSize="14"/>
                        </Grid>

                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="الثاني:" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding SecondOfficer}" Grid.Column="1" Padding="8" FontSize="14"/>
                        </Grid>

                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="الثالث:" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding ThirdOfficer}" Grid.Column="1" Padding="8" FontSize="14"/>
                        </Grid>

                        <!-- Notes -->
                        <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,20,0,5"/>
                        <TextBox Text="{Binding Notes}" 
                               Height="100" 
                               TextWrapping="Wrap" 
                               AcceptsReturn="True" 
                               VerticalScrollBarVisibility="Auto"
                               Padding="8"/>

                        <!-- Images Section -->
                        <TextBlock Text="🖼️ إدارة الصور" FontSize="16" FontWeight="Bold" Margin="0,30,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Button Content="📁 اختيار مجلد الصور" 
                                  Command="{Binding SelectImagesFolderCommand}"
                                  Background="#28A745" 
                                  Foreground="White" 
                                  Padding="10,5" 
                                  Margin="0,0,10,0"/>
                            
                            <Button Content="🖼️ إضافة صورة واحدة" 
                                  Command="{Binding AddSingleImageCommand}"
                                  Background="#17A2B8" 
                                  Foreground="White" 
                                  Padding="10,5"/>
                        </StackPanel>

                        <TextBlock Text="{Binding ImagesFolderPath, StringFormat='المجلد المحدد: {0}'}" 
                                 FontSize="12" 
                                 Foreground="Gray" 
                                 Margin="0,0,0,10"
                                 TextWrapping="Wrap"/>

                        <!-- Other Attachments -->
                        <TextBlock Text="📎 المرفقات الأخرى" FontSize="16" FontWeight="Bold" Margin="0,20,0,10"/>
                        
                        <Button Content="➕ إضافة مرفق" 
                              Command="{Binding AddOtherAttachmentCommand}"
                              Background="#FFC107" 
                              Foreground="#333333" 
                              Padding="10,5" 
                              Margin="0,0,0,10"/>

                        <DataGrid ItemsSource="{Binding OtherAttachments}"
                                AutoGenerateColumns="False"
                                CanUserAddRows="False"
                                Height="120"
                                GridLinesVisibility="Horizontal">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="*"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding FileType}" Width="60"/>
                                <DataGridTemplateColumn Header="إجراءات" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="🗑️" 
                                                  Command="{Binding DataContext.RemoveOtherAttachmentCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                  CommandParameter="{Binding}"
                                                  Background="#DC3545" 
                                                  Foreground="White" 
                                                  Padding="5,2"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Right Panel - Images Preview -->
                <Border Grid.Column="1" Background="#F8F9FA" Padding="15" CornerRadius="8">
                    <StackPanel>
                        <TextBlock Text="🖼️ معاينة الصور" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="600">
                            <ItemsControl ItemsSource="{Binding ImagePaths}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <WrapPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="#CCCCCC" BorderThickness="2" Margin="5" CornerRadius="5">
                                            <StackPanel>
                                                <Image Source="{Binding}" 
                                                       Width="200" 
                                                       Height="150" 
                                                       Stretch="UniformToFill"/>
                                                <StackPanel Orientation="Horizontal" Background="#333333" Margin="5">
                                                    <TextBlock Text="{Binding Converter={StaticResource FileNameConverter}}" 
                                                             Foreground="White" 
                                                             FontSize="10" 
                                                             Width="150"
                                                             TextTrimming="CharacterEllipsis"/>
                                                    <Button Content="❌" 
                                                          Command="{Binding DataContext.RemoveImageCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                          CommandParameter="{Binding}"
                                                          Background="Transparent" 
                                                          Foreground="White" 
                                                          BorderThickness="0"
                                                          Padding="2"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- Statistics -->
                        <Border Background="White" Padding="15" CornerRadius="5" Margin="0,20,0,0">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات" FontWeight="Bold" Margin="0,0,0,10"/>
                                <TextBlock Text="{Binding ImagePaths.Count, StringFormat='عدد الصور: {0}/6'}" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding OtherAttachments.Count, StringFormat='المرفقات الأخرى: {0}'}" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding Documentation.DocumentationDate, StringFormat='تاريخ التوثيق: {0:dd/MM/yyyy}'}" Margin="0,0,0,5"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="💾 حفظ التوثيق" 
                      Command="{Binding SaveDocumentationCommand}"
                      Background="#28A745" 
                      Foreground="White" 
                      Padding="20,10" 
                      FontWeight="Bold" 
                      FontSize="14"
                      Margin="0,0,15,0"/>
                
                <Button Content="👁️ معاينة التقرير" 
                      Command="{Binding PreviewReportCommand}"
                      Background="#17A2B8" 
                      Foreground="White" 
                      Padding="20,10" 
                      FontWeight="Bold" 
                      FontSize="14"
                      Margin="0,0,15,0"/>
                
                <Button Content="🖨️ طباعة" 
                      Command="{Binding PrintReportCommand}"
                      Background="#6C757D" 
                      Foreground="White" 
                      Padding="20,10" 
                      FontWeight="Bold" 
                      FontSize="14"
                      Margin="0,0,15,0"/>
                
                <Button Content="❌ إغلاق" 
                      Click="CloseButton_Click"
                      Background="#DC3545" 
                      Foreground="White" 
                      Padding="20,10" 
                      FontWeight="Bold" 
                      FontSize="14"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
